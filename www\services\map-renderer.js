// services/map-renderer.js
// Service for rendering the map and Pokemon markers

import { config } from '../config.js';
import { logger } from '../utils/logger.js';
import { gameState } from '../state/game-state.js';
import { getGermanPokemonName } from '../utils/pokemon-display-names.js';
import { LANDUSE_TYPE_MAPPING } from '../landuse-pokemon-types.js';
import { pokemonManager } from './pokemon-manager.js';
import { distanceMeters } from './utils/distance.js';

export class MapRenderer {
  constructor() {
    this.map = null;
    this.compassMarker = null;
    this.compassArrow = null;
    this.compassLabel = null;
    this.catchRadiusCircle = null;
    this.debugRadiusCircle = null;
    this.debugGridLayers = [];
    this.debugGridLabels = [];
    this.landusePolygonLayers = [];
    this.landusePolygonLabels = [];
  }

  /**
   * Initialize the map
   * @returns {Object} - The Leaflet map instance
   */
  initMap() {
    const { defaultCenter, defaultZoom, tileUrl, attribution } = config.map;

    this.map = L.map('map').setView(defaultCenter, defaultZoom);

    L.tileLayer(tileUrl, {
      attribution: attribution
    }).addTo(this.map);

    this.map.on('locationfound', this.onLocationFound.bind(this));
    this.map.on('locationerror', this.onLocationError.bind(this));

    // Store the map in gameState
    gameState.map = this.map;

    return this.map;
  }

  /**
   * Handle location found event
   * @param {Object} e - The location event
   */
  onLocationFound(e) {
    // This is just a stub - the actual implementation will be in main.js
    // as it involves complex game logic
  }

  /**
   * Handle location error event
   * @param {Object} e - The error event
   */
  onLocationError(e) {
    alert('Fehler beim Bestimmen der Position: ' + e.message);
  }

  /**
   * Render all Pokemon on the map
   * @param {boolean} recreateAll - Whether to recreate all markers
   */
  renderPokemons(recreateAll = false) {
    const { pokemons, pokemonMarkers, map, debugMode } = gameState;

    // If recreateAll is true, remove all markers and recreate
    if (recreateAll) {
      pokemonMarkers.forEach(entry => map.removeLayer(entry.marker));
      pokemonMarkers.clear();
    }

    if (debugMode) this.updateDebugInfo();

    // Track which Pokemon IDs are currently being displayed
    const currentIds = new Set(pokemons.map(p => p.id));
    const existingIds = new Set(pokemonMarkers.keys());

    // Remove markers for Pokemon that no longer exist
    for (const id of existingIds) {
      if (!currentIds.has(id)) {
        map.removeLayer(pokemonMarkers.get(id).marker);
        pokemonMarkers.delete(id);
      }
    }

    // Add or update markers for all Pokemon
    pokemons.forEach((pokemon) => {
      let entry = pokemonMarkers.get(pokemon.id);
      const iconUrl = pokemon.image_url || './src/PokemonSprites/25.png'; // Fallback to Pikachu
      let icon;

      if (pokemon.landuseSpecial && LANDUSE_TYPE_MAPPING[pokemon.landuseType]) {
        // Special icon with colored circle
        const color = LANDUSE_TYPE_MAPPING[pokemon.landuseType].color || '#0af';
        icon = L.divIcon({
          className: 'landuse-special-marker',
          iconSize: [config.ui.specialMarkerSize, config.ui.specialMarkerSize],
          iconAnchor: [config.ui.specialMarkerSize / 2, config.ui.specialMarkerSize / 2],
          popupAnchor: [0, -36],
          html: `<div style="position:relative;width:${config.ui.specialMarkerSize}px;height:${config.ui.specialMarkerSize}px;">
            <div style="position:absolute;left:4px;top:4px;width:${config.ui.specialMarkerSize - 8}px;height:${config.ui.specialMarkerSize - 8}px;border-radius:50%;background:${color};opacity:0.7;"></div>
            <img src="${pokemon.image_url || './src/PokemonSprites/25.png'}" style="position:absolute;left:0;top:0;width:${config.ui.specialMarkerSize}px;height:${config.ui.specialMarkerSize}px;z-index:2;">
          </div>`
        });
      } else {
        icon = L.icon({
          iconUrl: iconUrl,
          iconSize: [config.ui.markerSize, config.ui.markerSize],
          iconAnchor: [config.ui.markerSize / 2, config.ui.markerSize / 2],
          popupAnchor: [0, -36]
        });
      }

      if (!entry) {
        // Create new marker
        const marker = L.marker([pokemon.lat, pokemon.lng], { icon: icon });

        // Show all types as badges
        let typeBadges = '';
        if (Array.isArray(pokemon.types) && pokemon.types.length) {
          typeBadges = pokemon.types.map(type =>
            `<span class='pokemon-type-badge' style='background: var(--type-${type.toLowerCase()})'>${type}</span>`
          ).join(' ');
        } else if (pokemon.type) {
          typeBadges = `<span class='pokemon-type-badge' style='background: var(--type-${pokemon.type.toLowerCase()})'>${pokemon.type}</span>`;
        }

        // Get German name from pokedex for UI display only
        let displayName = 'Unbekannt';

        // Find the German name using dex_number (most reliable)
        let pokedexEntry = null;
        if (pokemon.dex_number) {
          pokedexEntry = gameState.pokedexData.find(p => p.dex_number === pokemon.dex_number);
        }

        // If not found by dex_number, try with evolved name (pokemon.name)
        if (!pokedexEntry && pokemon.name) {
          pokedexEntry = gameState.pokedexData.find(p =>
            p.name.toLowerCase() === pokemon.name.toLowerCase()
          );
        }

        // If still not found, try with base_name as fallback
        if (!pokedexEntry && pokemon.base_name) {
          pokedexEntry = gameState.pokedexData.find(p =>
            p.name.toLowerCase() === pokemon.base_name.toLowerCase()
          );
        }

        if (pokedexEntry && pokedexEntry.de) {
          displayName = pokedexEntry.de; // Use German name for display
        } else {
          // Fallback to English name if German name is not available
          displayName = pokemon.name || pokemon.base_name || 'Unbekannt';
        }

        const popupHtml = `<b>${displayName}</b><br>
          ${typeBadges}<br>
          Level: ${pokemon.level}<br>
          <button onclick="catchPokemonById('${pokemon.id}')">Begegnen</button>`;

        marker.bindPopup(popupHtml);
        marker.addTo(map);

        marker.on('popupopen', () => {
          if (pokemonMarkers.get(pokemon.id)) pokemonMarkers.get(pokemon.id).popupOpen = true;

          // Debug: Pokemon data
          try {
            // Create a basic debug object without the async data first
            const debugObj = {
              id: pokemon.id,
              base_name: pokemon.base_name,
              name: pokemon.name,
              level: pokemon.level,
              dex_number: pokemon.dex_number,
              types: pokemon.types,
              // Add landuse information to debug output
              landuse: {
                special: pokemon.landuseSpecial || false,
                type: pokemon.landuseType || null,
                typeName: pokemon.landuseTypeName || null,
                featureId: pokemon.featureId || null
              },
              currentForm: {} // Will be populated asynchronously
            };

            // For Logcat - log the basic info immediately
            const debugStr = '[POKEMON_DEBUG] ' + JSON.stringify(debugObj);
            if (window && window.cordova && window.cordova.plugins && window.cordova.plugins.logger) {
              window.cordova.plugins.logger.debug(debugStr);
            } else {
              logger.debug(debugStr);
            }

            // Log landuse information separately for better visibility
            logger.debug(`[LANDUSE_INFO] Pokemon ${pokemon.name || pokemon.base_name}: special=${pokemon.landuseSpecial || false}, type=${pokemon.landuseType || 'null'}, typeName=${pokemon.landuseTypeName || 'null'}`);

            // Log current display form data (already calculated during spawning)
            logger.debug(`Display form for ${pokemon.name}: {"name":"${pokemon.name}","sprite":"${pokemon.image_url}","dex_number":${pokemon.dex_number},"types":${JSON.stringify(pokemon.types)},"evolution_chain_id":${pokemon.evolution_chain_id || 'null'}}`);
          } catch (e) {
            logger.error('[POKEMON_DEBUG] ERROR', e);
          }

          // Show landuse info
          const popup = marker.getPopup();
          let landuseText = 'Landnutzung: unbekannt';

          // First check if we have a specific landuse type name
          if (pokemon.landuseTypeName) {
            landuseText = `Landnutzung: ${pokemon.landuseTypeName}`;
          }
          // Fall back to the landuse type if available
          else if (pokemon.landuseType) {
            landuseText = `Landnutzung: ${pokemon.landuseType}`;
          }

          let landuseHtml = `<br><span id="landuse-info-${pokemon.id}">${landuseText}</span>`;
          popup.setContent(popupHtml + landuseHtml);
          marker.openPopup();
        });

        marker.on('popupclose', () => {
          if (pokemonMarkers.get(pokemon.id)) pokemonMarkers.get(pokemon.id).popupOpen = false;
        });

        pokemonMarkers.set(pokemon.id, { marker, popupOpen: false });
      } else {
        // Update existing marker
        entry.marker.setLatLng([pokemon.lat, pokemon.lng]);

        // Show all types as badges
        let typeBadges = '';
        if (Array.isArray(pokemon.types) && pokemon.types.length) {
          typeBadges = pokemon.types.map(type =>
            `<span class='pokemon-type-badge' style='background: var(--type-${type.toLowerCase()})'>${type}</span>`
          ).join(' ');
        } else if (pokemon.type) {
          typeBadges = `<span class='pokemon-type-badge' style='background: var(--type-${pokemon.type.toLowerCase()})'>${pokemon.type}</span>`;
        }

        // Get German name from pokedex for UI display only
        let displayName = 'Unbekannt';

        // Find the German name using dex_number (most reliable)
        let pokedexEntry = null;
        if (pokemon.dex_number) {
          pokedexEntry = gameState.pokedexData.find(p => p.dex_number === pokemon.dex_number);
        }

        // If not found by dex_number, try with evolved name (pokemon.name)
        if (!pokedexEntry && pokemon.name) {
          pokedexEntry = gameState.pokedexData.find(p =>
            p.name.toLowerCase() === pokemon.name.toLowerCase()
          );
        }

        // If still not found, try with base_name as fallback
        if (!pokedexEntry && pokemon.base_name) {
          pokedexEntry = gameState.pokedexData.find(p =>
            p.name.toLowerCase() === pokemon.base_name.toLowerCase()
          );
        }

        if (pokedexEntry && pokedexEntry.de) {
          displayName = pokedexEntry.de; // Use German name for display
        } else {
          // Fallback to English name if German name is not available
          displayName = pokemon.name || pokemon.base_name || 'Unbekannt';
        }

        const popupHtml = `<b>${displayName}</b><br>
          ${typeBadges}<br>
          Level: ${pokemon.level}<br>
          <button onclick="catchPokemonById('${pokemon.id}')">Begegnen</button>`;

        if (entry.popupOpen) {
          // Show landuse info
          let landuseText = 'Landnutzung: unbekannt';

          // First check if we have a specific landuse type name
          if (pokemon.landuseTypeName) {
            landuseText = `Landnutzung: ${pokemon.landuseTypeName}`;
          }
          // Fall back to the landuse type if available
          else if (pokemon.landuseType) {
            landuseText = `Landnutzung: ${pokemon.landuseType}`;
          }

          entry.marker.setPopupContent(popupHtml + `<br><span id="landuse-info-${pokemon.id}">${landuseText}</span>`);
          entry.marker.openPopup();
        } else {
          entry.marker.setPopupContent(popupHtml);
        }
      }
    });
  }

  /**
   * Update debug info display
   */
  updateDebugInfo() {
    const { pokemons } = gameState;
    const debugInfoDiv = document.getElementById('debug-info');

    if (debugInfoDiv) {
      debugInfoDiv.textContent = `Pokémon auf der Map: ${pokemons.length}`;
    }

    // Update landuse polygon visualization in debug mode
    this.updateLandusePolygons();
  }

  /**
   * Show/hide landuse polygons based on debug mode
   */
  updateLandusePolygons() {
    const { map, debugMode } = gameState;

    if (debugMode) {
      this.showLandusePolygons();
    } else {
      this.hideLandusePolygons();
    }
  }

  /**
   * Show landuse polygons on the map
   */
  async showLandusePolygons() {
    const { map, landuseCache } = gameState;

    // Get landuse cache from gameState (set by pokemon spawner)
    if (!landuseCache?.data?.features) {
      logger.debug('[LANDUSE_DEBUG] No landuse cache available for visualization');
      return;
    }

    const features = landuseCache.data.features;
    logger.info(`[LANDUSE_DEBUG] 🗺️ Visualizing ${features.length} landuse polygons`);

    // Clear existing polygons
    this.hideLandusePolygons();

    try {
      features.forEach((feature, index) => {
        if (!feature.geometry || !feature.geometry.coordinates) return;

        const landuseType = feature.properties.value;
        const mapping = LANDUSE_TYPE_MAPPING[landuseType];
        const color = mapping?.color || '#888888';
        const typeName = mapping?.name || landuseType;

        // Create polygon
        const coords = feature.geometry.coordinates[0].map(coord => [coord[1], coord[0]]); // [lat, lng]

        const polygon = L.polygon(coords, {
          color: color,
          weight: 2,
          opacity: 0.8,
          fillColor: color,
          fillOpacity: 0.2,
          interactive: true
        }).addTo(map);

        // Add popup with landuse info
        polygon.bindPopup(`
          <div style="font-size: 12px;">
            <strong>Landuse Polygon</strong><br>
            <strong>Typ:</strong> ${typeName}<br>
            <strong>OSM-Tag:</strong> ${landuseType}<br>
            <strong>Element:</strong> ${feature.properties.element_type || 'way'}<br>
            <strong>OSM-ID:</strong> ${feature.properties.osm_id || 'unknown'}
          </div>
        `);

        this.landusePolygonLayers.push(polygon);

        // Add center label for larger polygons
        const bounds = polygon.getBounds();
        const center = bounds.getCenter();
        const area = this.calculatePolygonArea(coords);

        if (area > 1000) { // Only label polygons larger than 1000 m²
          const label = L.marker(center, {
            icon: L.divIcon({
              className: 'landuse-label',
              html: `<div style="background: rgba(255,255,255,0.8); padding: 2px 4px; border-radius: 3px; font-size: 10px; font-weight: bold; color: ${color}; border: 1px solid ${color};">${typeName}</div>`,
              iconSize: [60, 20],
              iconAnchor: [30, 10]
            }),
            interactive: false
          }).addTo(map);

          this.landusePolygonLabels.push(label);
        }
      });

      logger.info(`[LANDUSE_DEBUG] ✅ Added ${this.landusePolygonLayers.length} polygons and ${this.landusePolygonLabels.length} labels to map`);

    } catch (error) {
      logger.error('[LANDUSE_DEBUG] Error showing landuse polygons:', error);
    }
  }

  /**
   * Hide landuse polygons from the map
   */
  hideLandusePolygons() {
    const { map } = gameState;

    // Remove polygon layers
    this.landusePolygonLayers.forEach(layer => {
      try {
        if (map && map.hasLayer(layer)) {
          map.removeLayer(layer);
        }
      } catch (e) {
        logger.debug('Error removing landuse polygon layer:', e);
      }
    });
    this.landusePolygonLayers = [];

    // Remove label layers
    this.landusePolygonLabels.forEach(label => {
      try {
        if (map && map.hasLayer(label)) {
          map.removeLayer(label);
        }
      } catch (e) {
        logger.debug('Error removing landuse label layer:', e);
      }
    });
    this.landusePolygonLabels = [];

    logger.debug('[LANDUSE_DEBUG] 🗑️ Removed all landuse polygon visualizations');
  }

  /**
   * Calculate approximate area of a polygon in square meters
   * @param {Array} coords - Array of [lat, lng] coordinates
   * @returns {number} - Area in square meters
   */
  calculatePolygonArea(coords) {
    if (coords.length < 3) return 0;

    // Simple approximation using shoelace formula
    let area = 0;
    const n = coords.length;

    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n;
      area += coords[i][0] * coords[j][1];
      area -= coords[j][0] * coords[i][1];
    }

    area = Math.abs(area) / 2;

    // Convert from degrees to approximate meters (rough approximation)
    const metersPerDegree = 111000; // Approximate meters per degree at equator
    return area * metersPerDegree * metersPerDegree;
  }

  /**
   * Create or update the catch radius circle
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   */
  updateCatchRadiusCircle(lat, lng) {
    const { map } = gameState;

    if (!this.catchRadiusCircle) {
      this.catchRadiusCircle = L.circle([lat, lng], {
        radius: config.pokemon.catchRadius,
        color: '#1976d2',
        fillColor: '#1976d2',
        fillOpacity: 0.15,
        weight: 2,
        interactive: false
      }).addTo(map);

      gameState.catchRadiusCircle = this.catchRadiusCircle;
    } else {
      this.catchRadiusCircle.setLatLng([lat, lng]);
    }
  }

  /**
   * Create or update the compass marker
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {number} heading - Heading in degrees
   */
  updateCompassMarker(lat, lng, heading) {
    // Check if compass marker should be shown
    if (!config.ui.showCompassMarker) {
      // If compass marker exists but should not be shown, remove it
      if (this.compassMarker) {
        gameState.map.removeLayer(this.compassMarker);
        this.compassMarker = null;
        gameState.compassMarker = null;
      }
      if (this.compassLabel) {
        gameState.map.removeLayer(this.compassLabel);
        this.compassLabel = null;
        gameState.compassLabel = null;
      }
      return;
    }

    const { map } = gameState;
    const { compassSize } = config.ui;

    if (!this.compassMarker) {
      // Arrow SVG as a div icon
      const arrowIcon = L.divIcon({
        className: 'compass-arrow',
        html: `<div id="compass-arrow" style="width:${compassSize}px;height:${compassSize}px;transform:rotate(${heading}deg);"><svg width="${compassSize}" height="${compassSize}" viewBox="0 0 36 36"><polygon points="18,4 28,32 18,26 8,32" fill="rgba(255, 0, 0, 0.5)" stroke="black" stroke-width="2"/></svg></div>`,
        iconSize: [compassSize, compassSize],
        iconAnchor: [compassSize / 2, compassSize / 2]
      });

      this.compassMarker = L.marker([lat, lng], { icon: arrowIcon, interactive: false }).addTo(map);
      gameState.compassMarker = this.compassMarker;

      // Add a label
      this.compassLabel = L.marker([lat, lng], {
        icon: L.divIcon({
          className: 'compass-label',
          html: `<div id="compass-label" style="background:rgba(255,255,255,0.8);padding:2px 6px;border-radius:6px;font-size:14px;text-align:center;">${this.headingToDirection(heading)}</div>`,
          iconSize: [30, 20],
          iconAnchor: [15, -10]
        }),
        interactive: false
      }).addTo(map);

      gameState.compassLabel = this.compassLabel;
    } else {
      this.compassMarker.setLatLng([lat, lng]);
      this.compassLabel.setLatLng([lat, lng]);

      // Update rotation
      const arrowDiv = document.getElementById('compass-arrow');
      if (arrowDiv) {
        arrowDiv.style.transform = `rotate(${heading}deg)`;
      }

      // Update direction text
      const labelDiv = document.getElementById('compass-label');
      if (labelDiv) {
        labelDiv.textContent = this.headingToDirection(heading);
      }
    }
  }

  /**
   * Convert heading to compass direction
   * @param {number} heading - Heading in degrees
   * @returns {string} - Compass direction (N, NE, E, etc.)
   */
  headingToDirection(heading) {
    if (heading === null || heading === undefined) return 'N';
    const dirs = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
    const idx = Math.round(heading / 45) % 8;
    return dirs[idx];
  }

  /**
   * Setup map pan listener
   */
  setupMapPanListener() {
    if (!this.map) return;

    // Only actual dragging (panning) should show the center button
    this.map.on('dragstart', () => {
      if (gameState.hasCenteredOnUser) {
        this.showCenterButton();
        gameState.isCenteringActive = false;
      }
    });
  }

  /**
   * Show the center button
   */
  showCenterButton() {
    const btn = document.getElementById('center-fab');
    if (btn) btn.style.display = 'flex';
  }

  /**
   * Hide the center button
   */
  hideCenterButton() {
    const btn = document.getElementById('center-fab');
    if (btn) btn.style.display = 'none';
  }

  /**
   * Render all trainers on the map
   * @param {boolean} recreateAll - Whether to recreate all markers
   */
  async renderTrainers(recreateAll = false) {
    const { trainers, trainerMarkers, map } = gameState;

    // If recreateAll is true, remove all markers and recreate
    if (recreateAll) {
      trainerMarkers.forEach(entry => map.removeLayer(entry.marker));
      trainerMarkers.clear();
    }

    // Track which trainer IDs are currently being displayed
    const currentIds = new Set(trainers.map(t => t.id));
    const existingIds = new Set(trainerMarkers.keys());

    // Remove markers for trainers that no longer exist
    for (const id of existingIds) {
      if (!currentIds.has(id)) {
        map.removeLayer(trainerMarkers.get(id).marker);
        trainerMarkers.delete(id);
      }
    }

    // Check if player has required team size
    let playerHasRequiredTeam = false;
    try {
      await pokemonManager.initialize();
      const playerTeam = pokemonManager.getTeamPokemon();
      playerHasRequiredTeam = playerTeam && playerTeam.length === 6;
    } catch (e) {
      logger.warn('Could not check player team requirement:', e);
    }

    // Add or update markers for all trainers
    trainers.forEach((trainer) => {
      let entry = trainerMarkers.get(trainer.id);
      const iconUrl = trainer.getMapSpritePath();
      let icon;

      if (!entry) {
        // Create new marker
        icon = L.icon({
          iconUrl: iconUrl,
          iconSize: [config.ui.trainerSizeWidth, config.ui.trainerSizeHeight],
          iconAnchor: [config.ui.trainerSizeWidth / 2, config.ui.trainerSizeHeight / 2],
          popupAnchor: [0, -config.ui.trainerSizeHeight / 2]
        });

        const marker = L.marker([trainer.lat, trainer.lng], { icon: icon }).addTo(map);

        // Create team info for popup
        const teamInfo = this.createTrainerTeamInfo(trainer);

        // Check distance to trainer
        const distanceToTrainer = this.calculateDistanceToTrainer(trainer);
        const isDistanceFinite = Number.isFinite(distanceToTrainer);
        const withinChallengeRadius = isDistanceFinite && distanceToTrainer <= config.trainers.challengeRadius;

        // Button state based on team requirement AND distance
        const buttonDisabled = (!playerHasRequiredTeam || !withinChallengeRadius) ? 'disabled' : '';
        const buttonClass = 'challenge-button';

        // Warning text for different conditions
        let warningText = '';
        if (!playerHasRequiredTeam) {
          warningText = '<div class="team-requirement-warning">Du brauchst 6 Pokémon im Team!</div>';
        } else if (!isDistanceFinite) {
          warningText = '<div class="distance-warning">Position nicht verfügbar — Standortdaten fehlen</div>';
        } else if (!withinChallengeRadius) {
          warningText = `<div class="distance-warning">Zu weit entfernt! Komm näher (${distanceToTrainer.toFixed(0)}m / ${config.trainers.challengeRadius}m)</div>`;
        }

        const popupContent = `
          <div class="trainer-popup">
            <h3>${trainer.name}</h3>
            <p><strong>Typ:</strong> ${trainer.getDisplayName()}</p>
            <p><strong>Durchschnitts-Level:</strong> ${trainer.averageLevel}</p>
            <div class="trainer-team">
              <h4>Team:</h4>
              ${teamInfo}
            </div>
            <button onclick="challengeTrainer('${trainer.id}')" class="${buttonClass}" ${buttonDisabled}>
              Herausfordern
            </button>
            ${warningText}
          </div>
        `;

        marker.bindPopup(popupContent);

        // Store marker entry
        entry = { marker, popupOpen: false };
        trainerMarkers.set(trainer.id, entry);

        // Add popup event listeners
        marker.on('popupopen', () => {
          entry.popupOpen = true;
        });

        marker.on('popupclose', () => {
          entry.popupOpen = false;
        });

      } else {
        // Update existing marker position
        entry.marker.setLatLng([trainer.lat, trainer.lng]);
      }
    });
  }

  /**
   * Create team info HTML for trainer popup
   * @param {Trainer} trainer - The trainer
   * @returns {string} - HTML string for team info
   */
  createTrainerTeamInfo(trainer) {
    if (!trainer.team || trainer.team.length === 0) {
      return '<p>Kein Team</p>';
    }

    let teamHtml = '<div class="trainer-team-list">';
    trainer.team.forEach((pokemon) => {
      // Use German name for display
      const pokemonName = getGermanPokemonName(pokemon) || 'Unbekannt';
      const pokemonLevel = pokemon.level || 1;
      teamHtml += `
        <div class="trainer-pokemon">
          <span class="pokemon-name">${pokemonName}</span>
          <span class="pokemon-level">Lvl. ${pokemonLevel}</span>
        </div>
      `;
    });
    teamHtml += '</div>';

    return teamHtml;
  }

  /**
   * Calculate distance from player to trainer
   * @param {Trainer} trainer - The trainer
   * @returns {number} - Distance in meters
   */
  calculateDistanceToTrainer(trainer) {
    if (!gameState.lastUserLatLng || !trainer.lat || !trainer.lng) {
      return Infinity; // If no player position or trainer position, assume too far
    }

    // Use the same distance calculation as in main.js
    return distanceMeters(
      gameState.lastUserLatLng.lat,
      gameState.lastUserLatLng.lng,
      trainer.lat,
      trainer.lng
    );
  }

  /**
   * Update trainer popup buttons based on current player position
   * Called when player position changes
   */
  updateTrainerPopupButtons() {
    const { trainers, trainerMarkers } = gameState;

    trainers.forEach(trainer => {
      const entry = trainerMarkers.get(trainer.id);
      if (entry && entry.popupOpen) {
        // Popup is currently open, update it
        this.updateSingleTrainerPopup(trainer, entry);
      }
    });
  }

  /**
   * Update a single trainer's popup content
   * @param {Trainer} trainer - The trainer
   * @param {Object} entry - The marker entry
   */
  async updateSingleTrainerPopup(trainer, entry) {
    try {
      // Check if player has required team
      await pokemonManager.initialize();
      const playerTeam = pokemonManager.getTeamPokemon();
      const playerHasRequiredTeam = playerTeam && playerTeam.length === 6;

      // Check distance to trainer
      const distanceToTrainer = this.calculateDistanceToTrainer(trainer);
      const isDistanceFinite = Number.isFinite(distanceToTrainer);
      const withinChallengeRadius = isDistanceFinite && distanceToTrainer <= config.trainers.challengeRadius;

      // Create team info for popup
      const teamInfo = this.createTrainerTeamInfo(trainer);

      // Button state based on team requirement AND distance
      const buttonDisabled = (!playerHasRequiredTeam || !withinChallengeRadius) ? 'disabled' : '';
      const buttonClass = 'challenge-button';

      // Warning text for different conditions
      let warningText = '';
      if (!playerHasRequiredTeam) {
        warningText = '<div class="team-requirement-warning">Du brauchst 6 Pokémon im Team!</div>';
      } else if (!isDistanceFinite) {
        warningText = '<div class="distance-warning">Position nicht verfügbar — Standortdaten fehlen</div>';
      } else if (!withinChallengeRadius) {
        warningText = `<div class="distance-warning">Zu weit entfernt! Komm näher (${distanceToTrainer.toFixed(0)}m / ${config.trainers.challengeRadius}m)</div>`;
      }

      const popupContent = `
        <div class="trainer-popup">
          <h3>${trainer.name}</h3>
          <p><strong>Typ:</strong> ${trainer.getDisplayName()}</p>
          <p><strong>Durchschnitts-Level:</strong> ${trainer.averageLevel}</p>
          <div class="trainer-team">
            <h4>Team:</h4>
            ${teamInfo}
          </div>
          <button onclick="challengeTrainer('${trainer.id}')" class="${buttonClass}" ${buttonDisabled}>
            Herausfordern
          </button>
          ${warningText}
        </div>
      `;

      // Update the popup content
      entry.marker.setPopupContent(popupContent);

    } catch (error) {
      logger.error('Error updating trainer popup:', error);
    }
  }
}
